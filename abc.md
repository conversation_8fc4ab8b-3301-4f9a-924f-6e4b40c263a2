Please modify the provided code by following these steps and documenting your reasoning process. Ensure all responses are structured with clear markdown headers for each step. Do not execute any code or start any processes (e.g., servers) unless explicitly instructed.

### Step 1: Understand the Problem
- Read and summarize the problem description provided: [Describe the issue, including observed behavior, expected behavior, error messages, and relevant context, e.g., framework versions or environment].
- If the problem description is unclear, list specific questions to clarify requirements before proceeding.

### Step 2: Analyze Affected Code
- Identify all code sections potentially involved (e.g., front-end: HTML, CSS, JavaScript, React; back-end: Node.js, Express, database; configuration files).
- List all affected files with their paths and briefly explain their role in the problem.
- If no files are provided, suggest likely files based on the problem description and request confirmation.

### Step 3: Create Backup of Original Files
- Before modifying any files, copy each original file to a `Backup` folder in the project root.
- If the `Backup` folder does not exist, create it.
- Use the filename format: `[original_filename]_YYYYMMDD_HHMMSS` (e.g., `index.html_20250623_152300`).
- Use the `execute_command` tool to perform the copy operation, chaining commands with semicolons (`;`) for PowerShell compatibility (e.g., `mkdir Backup; copy index.html Backup/index.html_20250623_152300`).
- If the copy operation fails (e.g., file not found), report the error and halt further modifications until resolved.
- List all backed-up files with their new paths.

### Step 4: Diagnose Root Cause
- Analyze the front-end and back-end code step-by-step to identify the root cause of the problem.
- Check for common issues, such as:
  - Front-end: Incorrect data formatting, missing API headers, or UI rendering errors.
  - Back-end: API endpoint misconfiguration, data validation issues, or database query errors.
  - Integration: Mismatched data structures or HTTP status handling.
- Document findings, including any assumptions made during analysis.
- Check all code related to the modified value (e.g., hard-coded log messages, comments, calculations) to ensure consistency.

### Step 5: Propose and Apply Modifications
- Provide modification suggestions for each affected file, including:
  - **Code snippets**: Show the original and modified code side-by-side using markdown code blocks.
  - **Purpose**: Explain why each change is necessary.
  - **Expected effect**: Describe how the change addresses the problem.
  - **Dependencies**: If front-end changes require back-end updates (or vice versa), clearly specify and provide corresponding modifications.
- If new files need to be created, justify their necessity and provide their content.
- Ensure all modifications align with the project’s framework and coding standards.
- Validate modified code for syntax correctness and logical consistency.

### Step 6: Iterative Review of Changes
- After applying preliminary modifications, re-scan all related code (e.g., log messages, comments, dependent logic) to identify any missed changes.
- Simulate the program execution flow to verify that modifications resolve the issue and match expected behavior (e.g., PWM value and log message consistency).
- If inconsistencies or omissions are found (e.g., outdated log messages), return to Step 5 to fix them and document the changes.
- Repeat this review until no further changes are needed.
- Provide a summary of the review process, listing all checked components and confirming no omissions.

### Step 7: Update Memory Bank
- Synchronously update the memory bank with:
  - List of modified files and their final versions.
  - Summary of the problem, root cause, and applied fixes.
  - Backup file paths for reference.
- If the memory bank update fails, report the error and provide a fallback (e.g., local storage of changes).

### Step 8: Handle Errors and Edge Cases
- If any step fails (e.g., backup, analysis, modification), report the error with:
  - Error description.
  - Impact on the task.
  - Suggested next steps to resolve the issue.
- Address edge cases, such as missing dependencies, invalid inputs, or conflicting changes.

### Notes:
- Use semicolons (`;`) for command chaining in PowerShell; avoid `&&`.
- Do not modify files outside the specified scope unless justified and approved.
- Ensure timestamps in backup filenames use the system’s UTC time in `YYYYMMDD_HHMMSS` format.
- Structure all responses in markdown with labeled sections for clarity.